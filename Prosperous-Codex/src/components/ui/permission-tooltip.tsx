"use client";

import React from 'react';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Lock, Info } from 'lucide-react';

interface PermissionTooltipProps {
  children: React.ReactNode;
  hasPermission: boolean;
  permissionMessage: string;
  className?: string;
}

/**
 * Wrapper component that adds permission-based visual indicators and tooltips
 * to any child component. Provides consistent styling for disabled states.
 */
export function PermissionTooltip({
  children,
  hasPermission,
  permissionMessage,
  className = ""
}: PermissionTooltipProps) {
  if (hasPermission) {
    return <>{children}</>;
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`relative ${className}`}>
            {children}
            {/* Permission indicator overlay */}
            <div className="absolute top-1 right-1 z-10">
              <Lock className="h-3 w-3 text-gray-400 dark:text-gray-500" />
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
            <p className="text-xs">{permissionMessage}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Permission-aware button wrapper that provides consistent disabled styling
 */
interface PermissionButtonProps {
  children: React.ReactNode;
  hasPermission: boolean;
  permissionMessage: string;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  [key: string]: any; // Allow other props to pass through
}

export function PermissionButton({
  children,
  hasPermission,
  permissionMessage,
  onClick,
  disabled = false,
  className = "",
  ...props
}: PermissionButtonProps) {
  const isDisabled = disabled || !hasPermission;
  
  const button = (
    <button
      {...props}
      onClick={hasPermission ? onClick : undefined}
      disabled={isDisabled}
      className={`${className} ${
        !hasPermission 
          ? 'opacity-50 cursor-not-allowed' 
          : ''
      }`}
      title={!hasPermission ? permissionMessage : props.title}
    >
      {children}
    </button>
  );

  if (!hasPermission) {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            {button}
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-xs">
            <div className="flex items-start gap-2">
              <Lock className="h-4 w-4 text-amber-500 mt-0.5 flex-shrink-0" />
              <p className="text-xs">{permissionMessage}</p>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  return button;
}

/**
 * Visual indicator for read-only content
 */
interface ReadOnlyIndicatorProps {
  message: string;
  className?: string;
}

export function ReadOnlyIndicator({ message, className = "" }: ReadOnlyIndicatorProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`inline-flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400 ${className}`}>
            <Lock className="h-3 w-3" />
            <span>Read Only</span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="top" className="max-w-xs">
          <div className="flex items-start gap-2">
            <Info className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
            <p className="text-xs">{message}</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

/**
 * Consistent styling classes for permission-based states
 */
export const permissionStyles = {
  disabled: "opacity-50 cursor-not-allowed",
  readOnly: "cursor-default select-text",
  unauthorized: "border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50",
  tooltip: "max-w-xs text-xs"
} as const;

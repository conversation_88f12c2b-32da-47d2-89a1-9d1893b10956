"use client";

import React, { useState, useRef } from 'react';
import { Upload, FileText, Image as ImageIcon, File, X, Download, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { ProjectFile } from '@/lib/types/task-master';
import { useProjectPermissions, getPermissionExplanation } from '@/hooks/use-project-permissions';
import { Project, TeamMember } from '@/lib/types/task-master';

interface FileUploadProps {
  projectId: string;
  files: ProjectFile[];
  onFileUploaded: () => void;
  onFileDeleted: (fileId: number) => void;
  // New props for permission checking
  currentUserId?: string;
  project?: Project | null;
  teamMembers?: TeamMember[];
}

export default function FileUpload({
  projectId,
  files,
  onFileUploaded,
  onFileDeleted,
  currentUserId,
  project,
  teamMembers
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Get permissions for the current user
  const permissions = useProjectPermissions({
    currentUserId,
    project,
    teamMembers: teamMembers || []
  });

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles || selectedFiles.length === 0) return;

    const file = selectedFiles[0];
    uploadFile(file);
  };

  const uploadFile = async (file: File) => {
    setIsUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch(`/api/task-master/projects/${projectId}/files`, {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "File uploaded successfully"
        });
        onFileUploaded();
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to upload file",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
    }
  };

  const handleDeleteFile = async (fileId: number, fileName: string) => {
    try {
      const response = await fetch(`/api/task-master/files/${fileId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (response.ok) {
        toast({
          title: "Success",
          description: "File deleted successfully"
        });
        onFileDeleted(fileId);
      } else {
        toast({
          title: "Error",
          description: data.error || "Failed to delete file",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className="h-5 w-5 text-blue-600 dark:text-blue-400" />;
    } else if (fileType.includes('pdf') || fileType.includes('document')) {
      return <FileText className="h-5 w-5 text-red-600 dark:text-red-400" />;
    } else {
      return <File className="h-5 w-5 text-gray-600 dark:text-gray-400" />;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-semibold text-black dark:text-white">
          Files
        </h3>
        <Button
          variant="outline"
          size="sm"
          onClick={permissions.canUploadFiles ? () => fileInputRef.current?.click() : undefined}
          disabled={isUploading || !permissions.canUploadFiles}
          aria-label={
            !permissions.canUploadFiles
              ? getPermissionExplanation('canUploadFiles', permissions.userRole)
              : isUploading ? 'Uploading file...' : 'Upload file'
          }
          title={!permissions.canUploadFiles ? getPermissionExplanation('canUploadFiles', permissions.userRole) : undefined}
        >
          <Upload className="h-4 w-4 mr-2" aria-hidden="true" />
          {isUploading ? 'Uploading...' : 'Upload'}
        </Button>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
        accept="*/*"
        aria-label="Select file to upload"
      />

      {/* Drag and drop area */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          !permissions.canUploadFiles
            ? 'border-gray-200 bg-gray-50 dark:border-gray-700 dark:bg-gray-800/50 opacity-60'
            : dragActive
            ? 'border-[#5E6AD2] bg-[#5E6AD2]/5 dark:border-[#6E56CF] dark:bg-[#6E56CF]/5'
            : 'border-gray-300 dark:border-gray-600'
        }`}
        onDragEnter={permissions.canUploadFiles ? handleDrag : undefined}
        onDragLeave={permissions.canUploadFiles ? handleDrag : undefined}
        onDragOver={permissions.canUploadFiles ? handleDrag : undefined}
        onDrop={permissions.canUploadFiles ? handleDrop : undefined}
        role={permissions.canUploadFiles ? "button" : undefined}
        tabIndex={permissions.canUploadFiles ? 0 : undefined}
        aria-label={
          permissions.canUploadFiles
            ? "Drag and drop files here or click to select files"
            : getPermissionExplanation('canUploadFiles', permissions.userRole)
        }
        onKeyDown={permissions.canUploadFiles ? (e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            fileInputRef.current?.click();
          }
        } : undefined}
        title={!permissions.canUploadFiles ? getPermissionExplanation('canUploadFiles', permissions.userRole) : undefined}
      >
        <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" aria-hidden="true" />
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">
          {permissions.canUploadFiles
            ? "Drag and drop files here, or click to select"
            : "File upload requires team member permissions"
          }
        </p>
        <p className="text-xs text-gray-500 dark:text-gray-500">
          {permissions.canUploadFiles
            ? "Maximum file size: 10MB"
            : getPermissionExplanation('canUploadFiles', permissions.userRole)
          }
        </p>
        {permissions.canUploadFiles && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="mt-2"
          >
            Choose Files
          </Button>
        )}
      </div>

      {/* File list */}
      <div className="space-y-3">
        {files.length === 0 ? (
          <div className="text-center py-4 text-gray-500 dark:text-gray-400">
            <p className="text-sm">No files uploaded yet</p>
          </div>
        ) : (
          files.map((file) => (
            <div
              key={file.id}
              className="flex items-center gap-3 p-3 rounded-lg border border-gray-200 dark:border-[#2A2A2A] bg-white dark:bg-[#1A1A1A]"
            >
              <div className="h-10 w-10 rounded bg-gray-100 dark:bg-gray-800 flex items-center justify-center" aria-label={`File type: ${file.file_type || 'unknown'}`}>
                {getFileIcon(file.file_type || '')}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-black dark:text-white truncate">
                  {file.file_name}
                </p>
                <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                  <span>{formatFileSize(file.file_size || 0)}</span>
                  <span>•</span>
                  <span>Uploaded by {file.uploaded_by_username}</span>
                  <span>•</span>
                  <span>{formatDate(file.uploaded_at)}</span>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => {
                    // Create download link
                    const link = document.createElement('a');
                    link.href = `/api/task-master/files/${file.id}/download`;
                    link.download = file.file_name;
                    link.click();
                  }}
                  aria-label={`Download ${file.file_name}`}
                >
                  <Download className="h-4 w-4" aria-hidden="true" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                  onClick={() => handleDeleteFile(file.id, file.file_name)}
                  aria-label={`Delete ${file.file_name}`}
                >
                  <Trash2 className="h-4 w-4" aria-hidden="true" />
                </Button>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
